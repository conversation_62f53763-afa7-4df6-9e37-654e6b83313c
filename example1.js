import PDFFormFiller from './pdf-form-filler.js';
// import { surgicalReportConfig,  } from './form-config.js';

const surgicalReportConfig = {
  "operation_report.date": {
    "x": 88,
    "y": 691,
    "size": 11,
    "pageIndex": 0,
    "bold": false
  },
  "operation_report.time_commenced": {
    "x": 262,
    "y": 689,
    "size": 11,
    "pageIndex": 0,
    "bold": false
  },
  "operation_report.time_completed": {
    "x": 367,
    "y": 689,
    "size": 11,
    "pageIndex": 0,
    "bold": false
  },
  "operation_report.item_numbers": {
    "x": 416,
    "y": 667,
    "size": 11,
    "pageIndex": 0,
    "bold": false
  },
  "operation_report.medical_team.surgeon": {
    "x": 111,
    "y": 667,
    "size": 11,
    "pageIndex": 0,
    "bold": true
  },
  "operation_report.medical_team.assistant": {
    "x": 111,
    "y": 650,
    "size": 11,
    "pageIndex": 0,
    "bold": false
  },
  "operation_report.operative_diagnosis": {
    "x": 148,
    "y": 634,
    "size": 11,
    "pageIndex": 0,
    "bold": false,
    "maxWidth": 400
  },
  "operation_report.operation_performed": {
    "x": 148,
    "y": 616,
    "size": 11,
    "pageIndex": 0,
    "bold": true,
    "maxWidth": 400
  },
  "operation_report.operation_details": {
    "x": 70,
    "y": 570,
    "size": 10,
    "pageIndex": 0,
    "bold": false,
    "maxWidth": 480,
    "lineSpacing": 17.6,
    "maxLines": 15
  },
  "operation_report.vaginal_pack": {
    "type": "three-state-checkbox",
    "pageIndex": 0,
    "size": 12,
    "yesPosition": {
      "x": 122,
      "y": 309
    },
    "noPosition": {
      "x": 162,
      "y": 309
    },
    "naPosition": {
      "x": 202,
      "y": 309
    }
  },
  "operation_report.specimen.pathology": {
    "x": 120,
    "y": 291,
    "size": 12,
    "pageIndex": 0,
    "bold": false
  },
  "operation_report.specimen.discard": {
    "x": 205,
    "y": 290,
    "size": 12,
    "pageIndex": 0,
    "bold": false
  },
  "operation_report.specimen.description": {
    "x": 166,
    "y": 273,
    "size": 10,
    "pageIndex": 0,
    "bold": false,
    "maxWidth": 300
  },
  "operation_report.specimen.laboratory": {
    "x": 290,
    "y": 274,
    "size": 10,
    "pageIndex": 0,
    "bold": false
  },
  "post_procedure_orders.routine_observations": {
    "x": 70,
    "y": 215,
    "size": 10,
    "pageIndex": 0,
    "bold": false,
    "maxWidth": 450,
    "lineSpacing": 12,
    "maxLines": 10
  },
  "post_procedure_orders.position_in_bed": {
    "x": 126,
    "y": 124,
    "size": 10,
    "pageIndex": 0,
    "bold": false,
    "maxWidth": 300
  },
  "post_procedure_orders.dressings": {
    "x": 111,
    "y": 106,
    "size": 10,
    "pageIndex": 0,
    "bold": false,
    "maxWidth": 300
  },
  "post_procedure_orders.drain_tubes.present": {
    "x": 113,
    "y": 72,
    "size": 12,
    "pageIndex": 0,
    "bold": false
  },
  "post_procedure_orders.drain_tubes.type": {
    "x": 154,
    "y": 69,
    "size": 10,
    "pageIndex": 0,
    "bold": false,
    "maxWidth": 200
  }
}

async function basicExample() {
    console.log('🔄 Running Basic Example...');
  
    try {
      // Sample data (similar to your JSON files)
      const sampleData = {
        operation_report: {
          date: "15/1/25",
          time_commenced: "10:30",
          time_completed: "11:45",
          item_numbers: ["35633", "36754"],
          medical_team: {
            surgeon: "Dr. Lee",
            assistant: "Dr. Smith"
          },
          operative_diagnosis: "Endocervical polyp, fibroid uterus",
          operation_performed: "D&C, Hysteroscopy, Polypectomy",
          operation_details: [
            "1 Sterile procedure, Prep & drape Sterile procedure, Prep & drape Sterile procedure, Prep & drape",
            "2 Cervix dilated, hysteroscopy performed",
            "3 Endocervical polyp identified and removed",
            "4 Curettings obtained for pathology",
            "5 Procedure completed without complications",
            "6 Procedure completed without complications",
            "7 Procedure completed without complications",
            "8 Procedure completed without complications",
            "9 Procedure completed without complications",
            "10 Procedure completed without complications",
            "11 Procedure completed without complications",
            "12 Procedure completed without complications",
            "13 Procedure completed without complications",
            "14 Procedure completed without complications",
            "15 Procedure completed without complications",
            "16 Procedure completed without complications",
          ],
          vaginal_pack: null, // Test N/A state
          specimen: {
            pathology: true,
            discard: false,
            description: "Endocervical polyp tissue",
            laboratory: "Melb Path"
          }
        },
        post_procedure_orders: {
          routine_observations: [
            "RPAO - Routine post-anesthetic observations",
            "Monitor vital signs every 15 minutes x 4",
            "Diet as tolerated when fully awake"
          ],
          position_in_bed: "Semi-fowler's position",
          dressings: "No dressings required",
          drug_and_iv_therapy: "IV fluids as per chart, pain relief PRN",
          drain_tubes: {
            present: false,
            type: ""
          }
        }
      };
  
      // Initialize form filler
      const formFiller = new PDFFormFiller();
  
      // Load template (adjust path as needed)
      await formFiller.loadTemplate('OG_docs/BLANK_REPORT.pdf');
  
      // Fill form using configuration
      formFiller.fillForm(sampleData, surgicalReportConfig);
  
      // Add custom elements
      // addCustomElements(formFiller, sampleData);
  
      // Save result
      await formFiller.save('example1.pdf');
  
      console.log('✅ Basic example completed!');
  
    } catch (error) {
      console.error('❌ Basic example failed:', error.message);
    }
  }

  basicExample().catch(console.error);
  