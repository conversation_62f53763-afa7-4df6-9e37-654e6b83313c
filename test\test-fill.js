import fs from 'fs';
import PDFFormFiller from './pdf-form-filler.js';
import { surgicalReportConfig } from './form-config.js';

/**
 * Test script to verify PDF form filling functionality
 * This script tests the form filler with the existing data files
 */

async function testWithExistingData() {
  console.log('🧪 Testing PDF Form Filler with existing data...\n');

  const testCases = [
    {
      name: 'Test Case 1: resp-4-59.json',
      dataFile: 'resp-4-59.json',
      template: 'BLANK OP REPORT.pdf',
      output: 'test-output-4-59.pdf'
    },
    {
      name: 'Test Case 2: resp-5-41.json',
      dataFile: 'resp-5-41.json',
      template: 'BLANK OP REPORT.pdf',
      output: 'test-output-5-41.pdf'
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`🔄 Running ${testCase.name}...`);
      
      // Check if files exist
      if (!fs.existsSync(testCase.template)) {
        console.log(`⚠️  Template not found: ${testCase.template}, skipping...`);
        continue;
      }
      
      if (!fs.existsSync(testCase.dataFile)) {
        console.log(`⚠️  Data file not found: ${testCase.dataFile}, skipping...`);
        continue;
      }

      // Load data
      const data = JSON.parse(fs.readFileSync(testCase.dataFile, 'utf8'));
      console.log(`✓ Loaded data from ${testCase.dataFile}`);

      // Initialize form filler
      const formFiller = new PDFFormFiller();
      await formFiller.loadTemplate(testCase.template);

      // Get page info
      const dimensions = formFiller.getPageDimensions(0);
      console.log(`✓ Page dimensions: ${dimensions.width} x ${dimensions.height}`);

      // Fill form
      formFiller.fillForm(data, surgicalReportConfig);
      console.log('✓ Form filled with data');

      // Add test-specific elements
      addTestElements(formFiller, data, testCase.name);

      // Save result
      await formFiller.save(testCase.output);
      console.log(`✅ ${testCase.name} completed: ${testCase.output}\n`);

    } catch (error) {
      console.error(`❌ ${testCase.name} failed:`, error.message);
      console.log('');
    }
  }
}

function addTestElements(formFiller, data, testName) {
  // Add test identifier
  formFiller.drawText(`Test: ${testName}`, {
    x: 50,
    y: 30,
    size: 8,
    pageIndex: 0
  });

  // Add timestamp
  const timestamp = new Date().toLocaleString();
  formFiller.drawText(`Generated: ${timestamp}`, {
    x: 300,
    y: 30,
    size: 8,
    pageIndex: 0
  });

  // Add data summary
  const surgeon = data.operation_report?.medical_team?.surgeon || 'Unknown';
  const date = data.operation_report?.date || 'Unknown';
  
  formFiller.drawText(`Surgeon: ${surgeon} | Date: ${date}`, {
    x: 50,
    y: 15,
    size: 8,
    pageIndex: 0
  });
}

async function testCoordinateMapping() {
  console.log('🧪 Testing coordinate mapping...\n');

  try {
    const formFiller = new PDFFormFiller();
    await formFiller.loadTemplate('BLANK OP REPORT.pdf');

    const { width, height } = formFiller.getPageDimensions(0);
    console.log(`Page dimensions: ${width} x ${height}`);

    // Test various coordinate positions
    const testPositions = [
      { label: 'Top-Left', x: 50, y: height - 50 },
      { label: 'Top-Right', x: width - 100, y: height - 50 },
      { label: 'Center', x: width / 2, y: height / 2 },
      { label: 'Bottom-Left', x: 50, y: 50 },
      { label: 'Bottom-Right', x: width - 100, y: 50 }
    ];

    testPositions.forEach(pos => {
      formFiller.drawText(`${pos.label} (${pos.x}, ${pos.y})`, {
        x: pos.x,
        y: pos.y,
        size: 10,
        bold: true
      });
    });

    // Draw coordinate grid
    drawTestGrid(formFiller, width, height);

    await formFiller.save('test-coordinates.pdf');
    console.log('✅ Coordinate mapping test completed: test-coordinates.pdf\n');

  } catch (error) {
    console.error('❌ Coordinate mapping test failed:', error.message);
  }
}

function drawTestGrid(formFiller, width, height) {
  const step = 100;
  
  // Draw grid lines
  for (let x = 0; x <= width; x += step) {
    formFiller.drawLine({
      x1: x, y1: 0,
      x2: x, y2: height,
      thickness: 0.5
    });
    
    if (x % 200 === 0) {
      formFiller.drawText(String(x), { x: x + 2, y: 20, size: 6 });
    }
  }
  
  for (let y = 0; y <= height; y += step) {
    formFiller.drawLine({
      x1: 0, y1: y,
      x2: width, y2: y,
      thickness: 0.5
    });
    
    if (y % 200 === 0) {
      formFiller.drawText(String(y), { x: 5, y: y + 2, size: 6 });
    }
  }
}

async function testFormElements() {
  console.log('🧪 Testing form elements...\n');

  try {
    const formFiller = new PDFFormFiller();
    await formFiller.loadTemplate('BLANK OP REPORT.pdf');

    // Test text formatting
    formFiller.drawText('Normal Text', { x: 100, y: 700, size: 12 });
    formFiller.drawText('Bold Text', { x: 100, y: 680, size: 12, bold: true });
    formFiller.drawText('Large Text', { x: 100, y: 660, size: 16 });
    formFiller.drawText('Small Text', { x: 100, y: 640, size: 8 });

    // Test checkboxes
    formFiller.drawCheckbox(true, { x: 100, y: 600 });
    formFiller.drawText('Checked', { x: 120, y: 600, size: 10 });
    
    formFiller.drawCheckbox(false, { x: 100, y: 580 });
    formFiller.drawText('Unchecked', { x: 120, y: 580, size: 10 });

    // Test lines
    formFiller.drawLine({ x1: 100, y1: 550, x2: 300, y2: 550, thickness: 1 });
    formFiller.drawText('Signature Line', { x: 100, y: 535, size: 8 });

    // Test text wrapping
    const longText = 'This is a very long text that should wrap within the specified maximum width to demonstrate the text wrapping functionality.';
    formFiller.drawText(longText, {
      x: 100,
      y: 500,
      size: 10,
      maxWidth: 200,
      lineHeight: 12
    });

    await formFiller.save('test-elements.pdf');
    console.log('✅ Form elements test completed: test-elements.pdf\n');

  } catch (error) {
    console.error('❌ Form elements test failed:', error.message);
  }
}

async function runAllTests() {
  console.log('🚀 Starting PDF Form Filler Tests\n');
  
  await testWithExistingData();
  await testCoordinateMapping();
  await testFormElements();
  
  console.log('🎉 All tests completed!');
  console.log('Check the generated test-*.pdf files to verify results.');
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(console.error);
}

export { testWithExistingData, testCoordinateMapping, testFormElements };
