#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import PDFFormFiller from './pdf-form-filler.js';
import { surgicalReportConfig } from './form-config.js';

/**
 * Command Line Interface for PDF Form Filling and Extraction
 * Usage:
 *   Fill: node fill-form.js <template.pdf> <data.json> [output.pdf]
 *   Extract: node fill-form.js extract <filled.pdf> [output.json]
 */

async function main() {
  const args = process.argv.slice(2);

  if (args.length < 1) {
    showHelp();
    process.exit(1);
  }

  // Check if this is an extraction command
  if (args[0] === 'extract') {
    await handleExtraction(args.slice(1));
    return;
  }

  // Original fill command
  if (args.length < 2) {
    console.log('Usage: node fill-form.js <template.pdf> <data.json> [output.pdf]');
    console.log('       node fill-form.js extract <filled.pdf> [output.json]');
    console.log('');
    console.log('Examples:');
    console.log('  node fill-form.js template.pdf data.json filled-form.pdf');
    console.log('  node fill-form.js "BLANK OP REPORT.pdf" resp-4-59.json output.pdf');
    console.log('  node fill-form.js extract filled-form.pdf extracted-data.json');
    process.exit(1);
  }

  const templatePath = args[0];
  const dataPath = args[1];
  const outputPath = args[2] || generateOutputPath(templatePath, dataPath);

  try {
    // Validate input files
    if (!fs.existsSync(templatePath)) {
      throw new Error(`Template PDF not found: ${templatePath}`);
    }

    if (!fs.existsSync(dataPath)) {
      throw new Error(`Data file not found: ${dataPath}`);
    }

    console.log('🔄 Starting PDF form filling...');
    console.log(`📄 Template: ${templatePath}`);
    console.log(`📊 Data: ${dataPath}`);
    console.log(`💾 Output: ${outputPath}`);
    console.log('');

    // Load data
    const jsonData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
    console.log('✓ Data loaded successfully');

    // Initialize form filler
    const formFiller = new PDFFormFiller();
    await formFiller.loadTemplate(templatePath);

    // Get page dimensions for reference
    const dimensions = formFiller.getPageDimensions(0);
    console.log(`📐 Page dimensions: ${dimensions.width} x ${dimensions.height} points`);

    // Fill the form
    console.log('🖊️  Filling form fields...');
    formFiller.fillForm(jsonData, surgicalReportConfig);

    // Save the result
    await formFiller.save(outputPath);

    console.log('');
    console.log('✅ Form filling completed successfully!');
    console.log(`📁 Output saved to: ${outputPath}`);

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

/**
 * Handle PDF data extraction command
 * @param {Array} args - Command line arguments for extraction
 */
async function handleExtraction(args) {
  if (args.length < 1) {
    console.log('Usage: node fill-form.js extract <filled.pdf> [output.json]');
    console.log('');
    console.log('Examples:');
    console.log('  node fill-form.js extract filled-form.pdf extracted-data.json');
    console.log('  node fill-form.js extract "filled-report.pdf"');
    process.exit(1);
  }

  const filledPdfPath = args[0];
  const outputJsonPath = args[1] || generateExtractionOutputPath(filledPdfPath);

  try {
    // Validate input file
    if (!fs.existsSync(filledPdfPath)) {
      throw new Error(`Filled PDF not found: ${filledPdfPath}`);
    }

    console.log('🔄 Starting PDF data extraction...');
    console.log(`📄 Filled PDF: ${filledPdfPath}`);
    console.log(`💾 Output JSON: ${outputJsonPath}`);
    console.log('');

    // Initialize form filler for extraction
    const formFiller = new PDFFormFiller();
    await formFiller.loadTemplate(filledPdfPath);

    // Get page dimensions for reference
    const dimensions = formFiller.getPageDimensions(0);
    console.log(`📐 Page dimensions: ${dimensions.width} x ${dimensions.height} points`);

    // Extract form data
    console.log('🔍 Extracting form data...');
    const extractionResult = formFiller.extractFormData(surgicalReportConfig);

    // Save the extracted data
    fs.writeFileSync(outputJsonPath, JSON.stringify(extractionResult, null, 2));

    console.log('');
    console.log('✅ Data extraction completed!');
    console.log(`📁 Extracted data saved to: ${outputJsonPath}`);
    console.log('');
    console.log('📋 Extraction Summary:');
    console.log(`   - Method: ${extractionResult.extractionMethod}`);
    console.log(`   - Fields mapped: ${Object.keys(extractionResult.fieldMapping).length}`);
    console.log(`   - Timestamp: ${extractionResult.timestamp}`);
    console.log('');
    console.log('⚠️  Note: This extraction uses coordinate-based simulation.');
    console.log('   For production use, integrate with specialized PDF parsing libraries.');

  } catch (error) {
    console.error('❌ Extraction Error:', error.message);
    process.exit(1);
  }
}

/**
 * Generate output filename for extraction
 * @param {string} filledPdfPath - Filled PDF file path
 * @returns {string} Generated output path
 */
function generateExtractionOutputPath(filledPdfPath) {
  const baseName = path.basename(filledPdfPath, '.pdf');
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
  return `extracted-${baseName}-${timestamp}.json`;
}

/**
 * Generate output filename based on input files
 * @param {string} templatePath - Template file path
 * @param {string} dataPath - Data file path
 * @returns {string} Generated output path
 */
function generateOutputPath(templatePath, dataPath) {
  const templateName = path.basename(templatePath, '.pdf');
  const dataName = path.basename(dataPath, '.json');
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');

  return `filled-${templateName}-${dataName}-${timestamp}.pdf`;
}

/**
 * Display help information
 */
function showHelp() {
  console.log('PDF Form Filler - Surgical Operation Reports');
  console.log('');
  console.log('USAGE:');
  console.log('  Fill PDF:     node fill-form.js <template.pdf> <data.json> [output.pdf]');
  console.log('  Extract Data: node fill-form.js extract <filled.pdf> [output.json]');
  console.log('');
  console.log('FILL COMMAND:');
  console.log('  template.pdf  Path to the PDF template file');
  console.log('  data.json     Path to the JSON data file');
  console.log('  output.pdf    Optional output filename (auto-generated if not provided)');
  console.log('');
  console.log('EXTRACT COMMAND:');
  console.log('  filled.pdf    Path to a filled PDF file');
  console.log('  output.json   Optional output filename (auto-generated if not provided)');
  console.log('');
  console.log('EXAMPLES:');
  console.log('  Fill a form:');
  console.log('    node fill-form.js "BLANK OP REPORT.pdf" resp-4-59.json');
  console.log('    node fill-form.js template.pdf data.json filled-report.pdf');
  console.log('');
  console.log('  Extract data from filled PDF:');
  console.log('    node fill-form.js extract filled-report.pdf');
  console.log('    node fill-form.js extract filled-report.pdf extracted-data.json');
  console.log('');
  console.log('DATA FORMAT:');
  console.log('  The JSON file should contain surgical operation report data');
  console.log('  with the structure matching the provided examples.');
  console.log('');
  console.log('EXTRACTION NOTE:');
  console.log('  The extraction feature provides a framework for reverse-engineering');
  console.log('  form data from filled PDFs. For production use, consider integrating');
  console.log('  with specialized PDF parsing libraries like pdf-parse or PDF.js.');
}

// Handle help flags
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp();
  process.exit(0);
}

// Run the main function
main().catch(error => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
