import { getFontInfo, findBestSansSerifFont, listSystemFonts } from './font-utils.js';
import PDFFormFiller from './pdf-form-filler.js';

/**
 * Test script for font detection and Microsoft Sans Serif usage
 */

async function testFontDetection() {
  console.log('🔤 Testing Font Detection...\n');

  // Get font information
  const fontInfo = getFontInfo();
  console.log('📊 Font Information:');
  console.log(`   Platform: ${fontInfo.platform}`);
  console.log(`   Best Font: ${fontInfo.bestFont.name}`);
  console.log(`   Regular Path: ${fontInfo.bestFont.regular || 'Not found'}`);
  console.log(`   Bold Path: ${fontInfo.bestFont.bold || 'Not found'}`);
  console.log(`   Recommendation: ${fontInfo.recommendations[fontInfo.platform]}`);
  console.log('');

  // Find best sans serif font
  const bestFont = findBestSansSerifFont();
  console.log('🎯 Best Sans Serif Font:');
  console.log(`   Name: ${bestFont.name}`);
  console.log(`   Regular: ${bestFont.regular ? '✓ Found' : '❌ Not found'}`);
  console.log(`   Bold: ${bestFont.bold ? '✓ Found' : '❌ Not found'}`);
  console.log('');

  return bestFont;
}

async function testFontInPDF() {
  console.log('📄 Testing Font in PDF...\n');

  try {
    // Initialize form filler with font detection
    const formFiller = new PDFFormFiller();
    await formFiller.loadTemplate('OG_/BLANK OP REPORT.pdf');

    // Create a test PDF with different fonts
    const testData = {
      operation_report: {
        date: "Font Test - " + new Date().toLocaleDateString(),
        medical_team: {
          surgeon: "Dr. Microsoft Sans Serif Test",
          assistant: "Font Comparison"
        },
        operative_diagnosis: "Testing Microsoft Sans Serif vs Helvetica",
        operation_performed: "Font Rendering Test",
        operation_details: [
          "This text should be in Microsoft Sans Serif (if available)",
          "Or the best available sans-serif font on this system",
          "Fallback to Helvetica if no system fonts available"
        ]
      }
    };

    // Fill form with font test data
    formFiller.fillForm(testData, {
      'operation_report.date': { x: 150, y: 750, size: 12, pageIndex: 0 },
      'operation_report.medical_team.surgeon': { x: 150, y: 720, size: 12, bold: true, pageIndex: 0 },
      'operation_report.medical_team.assistant': { x: 150, y: 690, size: 11, pageIndex: 0 },
      'operation_report.operative_diagnosis': { x: 150, y: 660, size: 11, maxWidth: 400, pageIndex: 0 },
      'operation_report.operation_performed': { x: 150, y: 630, size: 11, bold: true, maxWidth: 400, pageIndex: 0 },
      'operation_report.operation_details': { x: 80, y: 580, size: 10, maxWidth: 450, lineSpacing: 15, maxLines: 10, pageIndex: 0 }
    });

    // Add font comparison text
    const { width, height } = formFiller.getPageDimensions(0);
    
    // Title
    formFiller.drawText('FONT COMPARISON TEST', {
      x: width / 2 - 100,
      y: height - 50,
      size: 16,
      bold: true
    });

    // Font information
    const fontInfo = getFontInfo();
    formFiller.drawText(`System: ${fontInfo.platform} | Font: ${fontInfo.bestFont.name}`, {
      x: 50,
      y: height - 80,
      size: 10
    });

    // Sample text in different sizes
    const sampleText = "Microsoft Sans Serif Sample Text";
    const sizes = [8, 10, 12, 14, 16];
    
    sizes.forEach((size, index) => {
      formFiller.drawText(`${sampleText} (${size}pt)`, {
        x: 50,
        y: 400 - (index * 25),
        size: size
      });
    });

    // Bold text samples
    formFiller.drawText('Bold Text Sample (Microsoft Sans Serif)', {
      x: 50,
      y: 250,
      size: 12,
      bold: true
    });

    await formFiller.save('font-test-output.pdf');
    console.log('✅ Font test PDF created: font-test-output.pdf\n');

  } catch (error) {
    console.error('❌ Font test failed:', error.message);
  }
}

async function listAvailableFonts() {
  console.log('📋 Available System Fonts:\n');

  try {
    const fonts = listSystemFonts();
    
    if (fonts.length === 0) {
      console.log('❌ No system fonts found');
      return;
    }

    // Filter for common fonts
    const commonFonts = fonts.filter(font => {
      const name = font.toLowerCase();
      return name.includes('arial') || 
             name.includes('microsoft') || 
             name.includes('calibri') || 
             name.includes('segoe') ||
             name.includes('sans');
    });

    console.log(`📊 Found ${fonts.length} total fonts, ${commonFonts.length} relevant fonts:`);
    
    commonFonts.slice(0, 20).forEach(font => {
      console.log(`   ${font}`);
    });

    if (commonFonts.length > 20) {
      console.log(`   ... and ${commonFonts.length - 20} more`);
    }

    console.log('');

  } catch (error) {
    console.error('❌ Failed to list fonts:', error.message);
  }
}

async function runFontTests() {
  console.log('🚀 Font Detection and Testing Suite\n');
  console.log('=' .repeat(50));
  console.log('');

  // Test 1: Font Detection
  const bestFont = await testFontDetection();

  // Test 2: List Available Fonts
  await listAvailableFonts();

  // Test 3: Font in PDF
  await testFontInPDF();

  // Summary
  console.log('📋 Summary:');
  if (bestFont.regular) {
    console.log(`✅ Microsoft Sans Serif or equivalent found: ${bestFont.name}`);
    console.log('✅ PDF forms will use the detected system font');
  } else {
    console.log('⚠️  No Microsoft Sans Serif found, using Helvetica fallback');
    console.log('💡 Consider installing Microsoft Core Fonts for better compatibility');
  }
  
  console.log('');
  console.log('🎉 Font testing completed!');
  console.log('Check font-test-output.pdf to see the font rendering results.');
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runFontTests().catch(console.error);
}

export { testFontDetection, testFontInPDF, listAvailableFonts };
