import fs from 'fs';
import path from 'path';
import os from 'os';

/**
 * Font Utilities for finding and loading system fonts
 * Specifically designed to find Microsoft Sans Serif and similar fonts
 */

/**
 * Common font paths on different operating systems
 */
const FONT_PATHS = {
  win32: [
    'C:/Windows/Fonts',
    'C:/WINDOWS/Fonts',
    path.join(os.homedir(), 'AppData/Local/Microsoft/Windows/Fonts')
  ],
  darwin: [
    '/System/Library/Fonts',
    '/Library/Fonts',
    path.join(os.homedir(), 'Library/Fonts')
  ],
  linux: [
    '/usr/share/fonts',
    '/usr/local/share/fonts',
    path.join(os.homedir(), '.fonts'),
    path.join(os.homedir(), '.local/share/fonts')
  ]
};

/**
 * Font file mappings for Microsoft Sans Serif and alternatives
 */
const FONT_MAPPINGS = {
  'microsoft-sans-serif': [
    'micross.ttf',
    'Microsoft Sans Serif.ttf',
    'microsoftsansserif.ttf',
    'ms-sans-serif.ttf'
  ],
  'arial': [
    'arial.ttf',
    'Arial.ttf',
    'ARIAL.TTF'
  ],
  'arial-bold': [
    'arialbd.ttf',
    'Arial Bold.ttf',
    'ARIALBD.TTF'
  ],
  'calibri': [
    'calibri.ttf',
    'Calibri.ttf',
    'CALIBRI.TTF'
  ],
  'calibri-bold': [
    'calibrib.ttf',
    'Calibri Bold.ttf',
    'CALIBRIB.TTF'
  ],
  'segoe-ui': [
    'segoeui.ttf',
    'Segoe UI.ttf',
    'SEGOEUI.TTF'
  ],
  'segoe-ui-bold': [
    'segoeuib.ttf',
    'Segoe UI Bold.ttf',
    'SEGOEUIB.TTF'
  ]
};

/**
 * Find font file on the system
 * @param {string} fontName - Font name key from FONT_MAPPINGS
 * @returns {string|null} Path to font file or null if not found
 */
export function findSystemFont(fontName) {
  const platform = os.platform();
  const fontPaths = FONT_PATHS[platform] || FONT_PATHS.linux;
  const fontFiles = FONT_MAPPINGS[fontName] || [];

  for (const basePath of fontPaths) {
    if (!fs.existsSync(basePath)) continue;

    for (const fontFile of fontFiles) {
      const fullPath = path.join(basePath, fontFile);
      if (fs.existsSync(fullPath)) {
        return fullPath;
      }
    }
  }

  return null;
}

/**
 * Find the best available font similar to Microsoft Sans Serif
 * @returns {Object} Object with regular and bold font paths
 */
export function findBestSansSerifFont() {
  const result = {
    regular: null,
    bold: null,
    name: 'Unknown'
  };

  // Priority order: Microsoft Sans Serif > Arial > Calibri > Segoe UI
  const fontPriority = [
    { name: 'Microsoft Sans Serif', regular: 'microsoft-sans-serif', bold: 'microsoft-sans-serif' },
    { name: 'Arial', regular: 'arial', bold: 'arial-bold' },
    { name: 'Calibri', regular: 'calibri', bold: 'calibri-bold' },
    { name: 'Segoe UI', regular: 'segoe-ui', bold: 'segoe-ui-bold' }
  ];

  for (const font of fontPriority) {
    const regularPath = findSystemFont(font.regular);
    const boldPath = findSystemFont(font.bold);

    if (regularPath) {
      result.regular = regularPath;
      result.bold = boldPath || regularPath; // Use regular if bold not found
      result.name = font.name;
      break;
    }
  }

  return result;
}

/**
 * Get font information and recommendations
 * @returns {Object} Font information object
 */
export function getFontInfo() {
  const platform = os.platform();
  const bestFont = findBestSansSerifFont();
  
  return {
    platform,
    bestFont,
    recommendations: {
      'win32': 'Microsoft Sans Serif, Arial, or Calibri should be available',
      'darwin': 'Arial or Helvetica should be available',
      'linux': 'Install Microsoft fonts package or use Liberation Sans'
    }
  };
}

/**
 * Create a font configuration for PDF form filler
 * @returns {Object} Font configuration
 */
export function createFontConfig() {
  const fontInfo = getFontInfo();
  const { bestFont } = fontInfo;

  return {
    hasCustomFont: !!bestFont.regular,
    fontName: bestFont.name,
    regularPath: bestFont.regular,
    boldPath: bestFont.bold,
    fallbackToStandard: !bestFont.regular
  };
}

/**
 * Download Microsoft Sans Serif font (for systems that don't have it)
 * Note: This is a placeholder - actual implementation would need proper licensing
 */
export function downloadMicrosoftFonts() {
  console.log('⚠️  Microsoft fonts are proprietary and require proper licensing.');
  console.log('💡 Alternatives:');
  console.log('   - Install Microsoft Core Fonts package');
  console.log('   - Use Liberation Sans (open source alternative)');
  console.log('   - Use system default sans-serif fonts');
}

/**
 * Validate font file
 * @param {string} fontPath - Path to font file
 * @returns {boolean} True if font file is valid
 */
export function validateFontFile(fontPath) {
  if (!fontPath || !fs.existsSync(fontPath)) {
    return false;
  }

  try {
    const stats = fs.statSync(fontPath);
    return stats.isFile() && stats.size > 0;
  } catch (error) {
    return false;
  }
}

/**
 * List all available fonts in system font directories
 * @returns {Array} Array of font file paths
 */
export function listSystemFonts() {
  const platform = os.platform();
  const fontPaths = FONT_PATHS[platform] || FONT_PATHS.linux;
  const fonts = [];

  for (const basePath of fontPaths) {
    if (!fs.existsSync(basePath)) continue;

    try {
      const files = fs.readdirSync(basePath);
      for (const file of files) {
        if (file.toLowerCase().endsWith('.ttf') || file.toLowerCase().endsWith('.otf')) {
          fonts.push(path.join(basePath, file));
        }
      }
    } catch (error) {
      // Skip directories we can't read
      continue;
    }
  }

  return fonts;
}

export default {
  findSystemFont,
  findBestSansSerifFont,
  getFontInfo,
  createFontConfig,
  downloadMicrosoftFonts,
  validateFontFile,
  listSystemFonts
};
