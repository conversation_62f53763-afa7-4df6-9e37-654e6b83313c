import fs from 'fs';
import PDFFormFiller from './pdf-form-filler.js';
import { surgicalReportConfig } from './form-config.js';

/**
 * Simple test to verify the extraction functionality works
 */

async function simpleExtractionTest() {
  console.log('🧪 Simple Extraction Test');
  console.log('========================\n');

  try {
    // Check if the example PDF exists
    const testPdf = 'example-filled-report.pdf';
    
    if (!fs.existsSync(testPdf)) {
      console.log(`❌ Test PDF not found: ${testPdf}`);
      console.log('   Please run: node example1.js first to create the test PDF');
      return;
    }

    console.log(`✓ Found test PDF: ${testPdf}`);

    // Initialize the extractor
    console.log('🔧 Initializing PDF extractor...');
    const extractor = new PDFFormFiller();
    
    // Load the PDF
    console.log('📄 Loading PDF...');
    await extractor.loadTemplate(testPdf);
    console.log('✓ PDF loaded successfully');

    // Perform extraction
    console.log('🔍 Performing extraction...');
    const result = extractor.extractFormData(surgicalReportConfig);
    
    console.log('✓ Extraction completed');
    console.log(`   Method: ${result.extractionMethod}`);
    console.log(`   Timestamp: ${result.timestamp}`);

    // Show some results
    console.log('\n📊 Extraction Results:');
    if (result.extractionResults) {
      console.log(`   Form fields found: ${result.extractionResults.formFieldsFound}`);
      console.log(`   Form fields extracted: ${result.extractionResults.formFieldsExtracted}`);
      console.log(`   Coordinate mappings: ${result.extractionResults.coordinateMappings}`);
      console.log(`   Errors: ${result.extractionResults.errors.length}`);
    }

    // Show sample data
    console.log('\n📋 Sample Extracted Data:');
    const data = result.jsonData;
    console.log(`   Date: "${data.operation_report.date}"`);
    console.log(`   Surgeon: "${data.operation_report.medical_team.surgeon}"`);
    console.log(`   Diagnosis: "${data.operation_report.operative_diagnosis}"`);

    // Save results
    const outputFile = 'example2-output.json';
    fs.writeFileSync(outputFile, JSON.stringify(result, null, 2));
    console.log(`\n💾 Results saved to: ${outputFile}`);

    console.log('\n✅ Simple extraction test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test
simpleExtractionTest().catch(console.error);
