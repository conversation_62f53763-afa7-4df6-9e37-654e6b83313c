// PDF Field Marker JavaScript
class PDFMarker {
    constructor() {
        this.pdfDoc = null;
        this.canvas = null;
        this.ctx = null;
        this.fields = [];
        this.pdfWidth = 0;
        this.pdfHeight = 0;
        this.currentFieldIndex = 0;
        this.fieldNames = [
            'operation_report.date',
            'operation_report.time_commenced',
            'operation_report.time_completed',
            'operation_report.item_numbers',
            'operation_report.medical_team.surgeon',
            'operation_report.medical_team.assistant',
            'operation_report.operative_diagnosis',
            'operation_report.operation_performed',
            'operation_report.operation_details',
            'operation_report.vaginal_pack',
            'operation_report.specimen.pathology',
            'operation_report.specimen.discard',
            'operation_report.specimen.description',
            'operation_report.specimen.laboratory',
            'post_procedure_orders.routine_observations',
            'post_procedure_orders.position_in_bed',
            'post_procedure_orders.dressings',
            'post_procedure_orders.drug_and_iv_therapy',
            'post_procedure_orders.drain_tubes.present',
            'post_procedure_orders.drain_tubes.type'
        ];

        this.init();
    }

    async init() {
        try {
            // Load the default PDF file
            await this.loadPDF('OG_docs/BLANK OP REPORT.pdf');
        } catch (error) {
            console.error('Error initializing PDF marker:', error);
            this.showError('Failed to load PDF file');
        }
    }

    async loadPDF(pdfPath) {
        try {
            const loadingTask = pdfjsLib.getDocument(pdfPath);
            this.pdfDoc = await loadingTask.promise;

            // Get the first page with NO SCALING
            const page = await this.pdfDoc.getPage(1);
            const viewport = page.getViewport({ scale: 1.0 });

            // Store PDF dimensions (no scaling)
            this.pdfWidth = viewport.width;
            this.pdfHeight = viewport.height;

            // Create canvas with exact PDF dimensions
            this.canvas = document.createElement('canvas');
            this.canvas.id = 'pdf-canvas';
            this.canvas.width = viewport.width;
            this.canvas.height = viewport.height;
            this.ctx = this.canvas.getContext('2d');

            // Render PDF at 1:1 scale
            const renderContext = {
                canvasContext: this.ctx,
                viewport: viewport
            };
            await page.render(renderContext).promise;

            // Add canvas to container
            const container = document.getElementById('pdf-container');
            container.innerHTML = '';
            container.appendChild(this.canvas);

            // Add event listeners
            this.setupEventListeners();

            // Show current field
            this.updateCurrentField();

        } catch (error) {
            console.error('Error loading PDF:', error);
            this.showError('Failed to load PDF: ' + error.message);
        }
    }

    setupEventListeners() {
        // Canvas click event
        this.canvas.addEventListener('click', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            const canvasX = e.clientX - rect.left;
            const canvasY = e.clientY - rect.top;

            // Direct 1:1 coordinate mapping (no scaling)
            const pdfX = canvasX;
            const pdfY = this.pdfHeight - canvasY; // Y starts from bottom

            this.addField(pdfX, pdfY, canvasX, canvasY);
        });

        // Mouse move event for coordinates display
        this.canvas.addEventListener('mousemove', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            const canvasX = e.clientX - rect.left;
            const canvasY = e.clientY - rect.top;

            // Direct 1:1 coordinate mapping (no scaling)
            const pdfX = canvasX;
            const pdfY = this.pdfHeight - canvasY; // Y starts from bottom

            document.getElementById('coords-display').innerHTML =
                `Mouse: x: ${Math.round(pdfX)}, y: ${Math.round(pdfY)}`;
        });

        // Keyboard events for navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight' || e.key === 'Space') {
                e.preventDefault();
                this.nextField();
            } else if (e.key === 'ArrowLeft') {
                e.preventDefault();
                this.previousField();
            }
        });
    }

    addField(pdfX, pdfY, canvasX, canvasY) {
        const fieldName = this.fieldNames[this.currentFieldIndex];

        const field = {
            id: Date.now(),
            name: fieldName,
            x: Math.round(pdfX),
            y: Math.round(pdfY),
            canvasX: canvasX,
            canvasY: canvasY
        };

        this.fields.push(field);
        this.createMarker(field);

        console.log(`Added field: ${field.name} at (${field.x}, ${field.y})`);

        // Move to next field
        this.nextField();
    }

    updateCurrentField() {
        const currentField = this.fieldNames[this.currentFieldIndex];
        const progress = `${this.currentFieldIndex + 1}/${this.fieldNames.length}`;

        document.getElementById('active-field-display').innerHTML =
            `<strong>ACTIVE FIELD (${progress}):</strong><br>${currentField}<br><br>Click on PDF to mark this field<br>Use ← → arrows to navigate`;
    }

    nextField() {
        if (this.currentFieldIndex < this.fieldNames.length - 1) {
            this.currentFieldIndex++;
        } else {
            this.currentFieldIndex = 0; // Loop back to start
        }
        this.updateCurrentField();
    }

    previousField() {
        if (this.currentFieldIndex > 0) {
            this.currentFieldIndex--;
        } else {
            this.currentFieldIndex = this.fieldNames.length - 1; // Loop to end
        }
        this.updateCurrentField();
    }

    createMarker(field) {
        const marker = document.createElement('div');
        marker.className = 'marker';

        // Simple direct positioning relative to canvas
        const container = document.getElementById('pdf-container');
        const canvasRect = this.canvas.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();

        // Calculate marker position relative to container
        const markerX = canvasRect.left - containerRect.left + field.canvasX - 4;
        const markerY = canvasRect.top - containerRect.top + field.canvasY - 4;

        marker.style.left = markerX + 'px';
        marker.style.top = markerY + 'px';
        marker.title = `${field.name} (${field.x}, ${field.y})`;
        marker.dataset.fieldId = field.id;

        // Add to container
        container.appendChild(marker);
    }

    exportConfig() {
        if (this.fields.length === 0) {
            alert('No fields to export!');
            return;
        }

        const config = {
            pdfFile: 'OG_docs/BLANK OP REPORT.pdf',
            pdfDimensions: {
                width: Math.round(this.pdfWidth),
                height: Math.round(this.pdfHeight)
            },
            fields: this.fields.map(field => ({
                name: field.name,
                x: field.x,
                y: field.y
            })),
            exportDate: new Date().toISOString(),
            totalFields: this.fields.length
        };

        const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'pdf-field-config.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log('Config exported:', config);

        // Update coords display to show export success
        document.getElementById('coords-display').innerHTML =
            `✅ Exported ${this.fields.length} fields<br>to pdf-field-config.json`;

        setTimeout(() => {
            document.getElementById('coords-display').innerHTML =
                `Total fields: ${this.fields.length}<br>Click to add more`;
        }, 3000);
    }

    loadConfig() {
        const fileInput = document.getElementById('config-file-input');
        fileInput.onchange = (e) => this.handleConfigFile(e);
        fileInput.click();
    }

    handleConfigFile(event) {
        const file = event.target.files[0];
        if (!file) return;

        if (!file.name.endsWith('.json')) {
            alert('Please select a JSON configuration file.');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const config = JSON.parse(e.target.result);
                this.restoreFields(config);
            } catch (error) {
                console.error('Error parsing config file:', error);
                alert('Invalid JSON configuration file. Please check the file format.');
            }
        };
        reader.readAsText(file);
    }

    restoreFields(config) {
        try {
            let fieldsToLoad = [];

            // Handle different config formats
            if (config.fields && Array.isArray(config.fields)) {
                // New format: { fields: [...] }
                fieldsToLoad = config.fields;
            } else if (typeof config === 'object' && !Array.isArray(config)) {
                // Old format: { "field.name": { x: ..., y: ... }, ... }
                fieldsToLoad = Object.entries(config).map(([name, coords]) => ({
                    name: name,
                    x: coords.x,
                    y: coords.y
                }));
            } else {
                throw new Error('Invalid config format: expected object with fields array or field mappings');
            }

            if (fieldsToLoad.length === 0) {
                throw new Error('No valid fields found in configuration');
            }

            // Clear existing fields
            this.clearFields();

            // Restore fields from config
            fieldsToLoad.forEach((fieldConfig, index) => {
                if (!fieldConfig.name || fieldConfig.x === undefined || fieldConfig.y === undefined) {
                    console.warn(`Skipping invalid field at index ${index}:`, fieldConfig);
                    return;
                }

                // Calculate canvas coordinates from PDF coordinates
                const canvasX = fieldConfig.x;
                const canvasY = this.pdfHeight - fieldConfig.y; // Convert from bottom-up to top-down

                const field = {
                    id: Date.now() + index, // Ensure unique IDs
                    name: fieldConfig.name,
                    x: fieldConfig.x,
                    y: fieldConfig.y,
                    canvasX: canvasX,
                    canvasY: canvasY
                };

                this.fields.push(field);
                this.createMarker(field);
            });

            // Update current field index to continue from where we left off
            this.currentFieldIndex = this.fields.length < this.fieldNames.length ?
                this.fields.length : 0;
            this.updateCurrentField();

            // Show success message
            document.getElementById('coords-display').innerHTML =
                `✅ Loaded ${this.fields.length} fields<br>from configuration`;

            setTimeout(() => {
                document.getElementById('coords-display').innerHTML =
                    `Total fields: ${this.fields.length}<br>Click to add more`;
            }, 3000);

            console.log('Config loaded successfully:', config);

        } catch (error) {
            console.error('Error restoring fields:', error);
            alert('Error loading configuration: ' + error.message);
        }
    }

    clearFields() {
        // Remove all existing markers from the DOM
        const markers = document.querySelectorAll('.marker');
        markers.forEach(marker => marker.remove());

        // Clear the fields array
        this.fields = [];

        console.log('All fields cleared');
    }

    showError(message) {
        const container = document.getElementById('pdf-container');
        container.innerHTML = `
            <div class="loading" style="color: #e74c3c;">
                ❌ Error: ${message}
            </div>
        `;
    }
}

// Global functions
function exportConfig() {
    pdfMarker.exportConfig();
}

function loadConfig() {
    pdfMarker.loadConfig();
}

// Initialize when page loads
let pdfMarker;
document.addEventListener('DOMContentLoaded', () => {
    pdfMarker = new PDFMarker();
});
