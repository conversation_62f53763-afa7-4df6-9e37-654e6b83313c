<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Field Marker - BLANK OP REPORT</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #f0f0f0;
            overflow: hidden;
        }

        .pdf-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            overflow: auto;
            text-align: center;
            padding: 20px;
        }

        #pdf-canvas {
            cursor: crosshair;
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .marker {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #e74c3c;
            border: 2px solid white;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            z-index: 10;
        }

        .marker:hover {
            transform: scale(1.5);
            background: #c0392b;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
            font-family: Arial, sans-serif;
        }

        .export-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
        }

        .export-btn:hover {
            background: #229954;
        }

        .coords-display {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            z-index: 1000;
        }

        .active-field-display {
            position: fixed;
            top: 80px;
            left: 20px;
            background: rgba(52, 152, 219, 0.9);
            color: white;
            padding: 15px;
            border-radius: 5px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            z-index: 1000;
            max-width: 300px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="coords-display" id="coords-display">
        Click on PDF to mark fields
    </div>

    <div class="active-field-display" id="active-field-display">
        Loading...
    </div>

    <button class="export-btn" onclick="exportConfig()">Export Config</button>
    <button class="export-btn" onclick="loadConfig()" style="right: 140px; background: #3498db;">Load Config</button>
    <input type="file" id="config-file-input" accept=".json" style="display: none;" />

    <div class="pdf-container" id="pdf-container">
        <div class="loading" id="loading">
            📄 Loading PDF...
        </div>
    </div>

    <!-- PDF.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        // Configure PDF.js worker
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
    </script>

    <script src="pdf-marker.js"></script>
</body>
</html>
