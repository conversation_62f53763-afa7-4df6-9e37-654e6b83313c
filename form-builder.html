<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Form Builder - Surgical Operation Report</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin-bottom: 0.5rem;
        }

        .header p {
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .container {
            display: flex;
            height: calc(100vh - 80px);
            gap: 1rem;
            padding: 1rem;
        }

        .sidebar {
            width: 350px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow-y: auto;
            padding: 1rem;
        }

        .main-content {
            flex: 1;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            background: #34495e;
            color: white;
            padding: 1rem;
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn.secondary {
            background: #95a5a6;
        }

        .btn.secondary:hover {
            background: #7f8c8d;
        }

        .btn.success {
            background: #27ae60;
        }

        .btn.success:hover {
            background: #229954;
        }

        .btn.danger {
            background: #e74c3c;
        }

        .btn.danger:hover {
            background: #c0392b;
        }

        .pdf-container {
            flex: 1;
            position: relative;
            overflow: auto;
            background: #ecf0f1;
            padding: 2rem;
            text-align: center;
        }

        #pdf-canvas {
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            cursor: crosshair;
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .field-marker {
            position: absolute;
            background: rgba(52, 152, 219, 0.3);
            border: 2px solid #3498db;
            border-radius: 4px;
            cursor: move;
            min-width: 20px;
            min-height: 20px;
            transition: all 0.2s ease;
            user-select: none;
        }

        .field-marker:hover {
            background: rgba(52, 152, 219, 0.5);
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.4);
        }

        .field-marker.selected {
            border-color: #e74c3c;
            background: rgba(231, 76, 60, 0.3);
            box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
        }

        .field-marker.selected:hover {
            background: rgba(231, 76, 60, 0.5);
            transform: scale(1.1);
        }

        .field-label {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            z-index: 10;
        }

        .section {
            margin-bottom: 1.5rem;
        }

        .section h3 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #ecf0f1;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.25rem;
            font-weight: 500;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .field-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .field-item {
            padding: 0.75rem;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.2s;
        }

        .field-item:hover {
            background: #f8f9fa;
        }

        .field-item.selected {
            background: #e3f2fd;
            border-left: 4px solid #3498db;
        }

        .field-item .field-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .field-item .field-coords {
            font-size: 0.8rem;
            color: #7f8c8d;
            margin-top: 0.25rem;
        }

        .status {
            background: #d4edda;
            color: #155724;
            padding: 0.75rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
        }

        .coordinates {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            color: #7f8c8d;
        }

        .zoom-controls {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .zoom-level {
            color: white;
            font-size: 0.9rem;
            min-width: 60px;
            text-align: center;
        }

        kbd {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            box-shadow: 0 1px 0 rgba(0,0,0,0.2), inset 0 0 0 2px #fff;
            color: #495057;
            display: inline-block;
            font-family: 'Courier New', monospace;
            font-size: 0.75em;
            font-weight: bold;
            line-height: 1;
            padding: 2px 4px;
            white-space: nowrap;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                height: auto;
            }

            .sidebar {
                width: 100%;
                order: 2;
            }

            .main-content {
                order: 1;
                height: 60vh;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📄 PDF Form Builder</h1>
        <p>Create precise form configurations for the Surgical Operation Report PDF</p>
    </div>

    <div class="container">
        <div class="sidebar">
            <div class="section">
                <h3>📁 PDF File</h3>
                <div class="form-group">
                    <input type="file" id="pdf-file" accept=".pdf" />
                </div>
                <button class="btn" onclick="loadDefaultPDF()">Load Default PDF</button>
            </div>

            <div class="section">
                <h3>🎯 Current Field</h3>
                <div style="background: #e8f4fd; padding: 0.75rem; border-radius: 4px; margin-bottom: 1rem; border-left: 4px solid #3498db;">
                    <small style="color: #2980b9;">
                        💡 <strong>Tips:</strong><br>
                        • Click on PDF to create fields<br>
                        • Drag field markers to reposition<br>
                        • Hover over markers to see field names<br>
                        • Use 🎯 button to reset zoom for accurate positioning<br>
                        • Use arrow keys to move selected fields
                    </small>
                </div>
                <div style="background: #f0f8ff; padding: 0.75rem; border-radius: 4px; margin-bottom: 1rem; border-left: 4px solid #6c5ce7;">
                    <small style="color: #5a4fcf;">
                        ⌨️ <strong>Keyboard Controls:</strong><br>
                        • <kbd>↑↓←→</kbd> Move field by 1 unit<br>
                        • <kbd>Shift</kbd> + <kbd>↑↓←→</kbd> Move by 10 units (coarse)<br>
                        • <kbd>Ctrl</kbd> + <kbd>↑↓←→</kbd> Move by 0.5 units (fine)
                    </small>
                </div>
                <div style="background: #fff3cd; padding: 0.75rem; border-radius: 4px; margin-bottom: 1rem; border-left: 4px solid #f39c12;">
                    <small style="color: #856404;">
                        ⚠️ <strong>Zoom & Coordinates:</strong><br>
                        Zoom level affects field positioning. For most accurate results, use 100% zoom when placing fields.
                    </small>
                </div>
                <div id="field-editor">
                    <p style="color: #7f8c8d; font-style: italic;">Click on the PDF to create a field</p>
                </div>
            </div>

            <div class="section">
                <h3>📋 Field List</h3>
                <div class="field-list" id="field-list">
                    <div style="padding: 1rem; text-align: center; color: #7f8c8d;">
                        No fields created yet
                    </div>
                </div>
            </div>

            <div class="section">
                <h3>💾 Export/Import</h3>
                <button class="btn success" onclick="exportConfig()">📤 Export Config</button>
                <button class="btn secondary" onclick="importConfig()">📥 Import Config</button>
                <button class="btn" onclick="loadExistingConfig()">📋 Load Existing</button>
                <button class="btn" onclick="loadTestConfig()" style="background: #9b59b6;">🧪 Load Test Config</button>
                <input type="file" id="config-file" accept=".json" style="display: none;" />
            </div>

            <div class="section">
                <h3>📝 Fill Out Form</h3>
                <div style="background: #fff3cd; padding: 0.75rem; border-radius: 4px; margin-bottom: 1rem; border-left: 4px solid #e67e22;">
                    <small style="color: #856404;">
                        <strong>📝 Screen Fill Feature:</strong><br>
                        • Uses resp-4-59.json data<br>
                        • Shows filled form on screen<br>
                        • Blue text overlay on PDF<br>
                        • Use 🧹 Clear Form to remove<br>
                        • Perfect for testing field positions
                    </small>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="toolbar">
                <div class="zoom-controls">
                    <button class="btn secondary" onclick="zoomOut()">🔍-</button>
                    <span class="zoom-level" id="zoom-level">100%</span>
                    <button class="btn secondary" onclick="zoomIn()">🔍+</button>
                    <button class="btn secondary" onclick="resetZoom()" title="Reset to 100% for accurate positioning">🎯</button>
                </div>
                <button class="btn" onclick="toggleGrid()">📐 Grid</button>
                <button class="btn secondary" onclick="clearFields()">🗑️ Clear All</button>
                <button class="btn success" onclick="testForm()">🧪 Test Form</button>
                <button class="btn" onclick="fillOutForm()" style="background: #e67e22;" title="Fill form with resp-4-59.json data on screen">📝 Fill Out Form</button>
                <button class="btn secondary" onclick="clearFormData()" title="Clear filled form data from screen">🧹 Clear Form</button>
                <button class="btn" onclick="downloadFilledPDF()" style="background: #27ae60;" title="Download filled PDF file">💾 Download PDF</button>
                <div style="margin-left: auto;">
                    <span class="coordinates" id="mouse-coords">x: 0, y: 0</span>
                </div>
            </div>

            <div class="pdf-container" id="pdf-container">
                <div class="loading">
                    📄 Load a PDF file to start creating form fields
                </div>
            </div>
        </div>
    </div>

    <!-- PDF.js Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        // Configure PDF.js worker
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
    </script>

    <!-- jsPDF Library for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <script src="form-builder.js"></script>
</body>
</html>
