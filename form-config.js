/**
 * Form Configuration for Surgical Operation Report
 * Maps JSON data fields to PDF coordinates and formatting options
 */

export const surgicalReportConfig = {
  // Operation Report Section
  'operation_report.date': {
    x: 150,
    y: 750,
    size: 11,
    pageIndex: 0
  },

  'operation_report.time_commenced': {
    x: 300,
    y: 750,
    size: 11,
    pageIndex: 0
  },

  'operation_report.time_completed': {
    x: 450,
    y: 750,
    size: 11,
    pageIndex: 0
  },

  'operation_report.item_numbers': {
    x: 150,
    y: 720,
    size: 11,
    pageIndex: 0
  },

  // Medical Team
  'operation_report.medical_team.surgeon': {
    x: 150,
    y: 690,
    size: 11,
    bold: true,
    pageIndex: 0
  },

  'operation_report.medical_team.assistant': {
    x: 350,
    y: 690,
    size: 11,
    pageIndex: 0
  },

  // Diagnosis and Operation
  'operation_report.operative_diagnosis': {
    x: 150,
    y: 660,
    size: 11,
    maxWidth: 400,
    pageIndex: 0
  },

  'operation_report.operation_performed': {
    x: 150,
    y: 630,
    size: 11,
    maxWidth: 400,
    bold: true,
    pageIndex: 0
  },

  // Operation Details (array field)
  'operation_report.operation_details': {
    x: 80,
    y: 580,
    size: 10,
    maxWidth: 450,
    lineSpacing: 15,
    maxLines: 20,
    pageIndex: 0
  },

  // Specimen Information
  'operation_report.specimen.pathology': {
    x: 200,
    y: 200,
    size: 12,
    pageIndex: 0
  },

  'operation_report.specimen.discard': {
    x: 300,
    y: 200,
    size: 12,
    pageIndex: 0
  },

  'operation_report.specimen.description': {
    x: 150,
    y: 170,
    size: 10,
    maxWidth: 300,
    pageIndex: 0
  },

  'operation_report.specimen.laboratory': {
    x: 150,
    y: 140,
    size: 10,
    pageIndex: 0
  },

  // Vaginal Pack
  'operation_report.vaginal_pack': {
    x: 400,
    y: 200,
    size: 12,
    pageIndex: 0
  },

  // Post Procedure Orders (on same page for now)
  'post_procedure_orders.routine_observations': {
    x: 80,
    y: 300,
    size: 10,
    maxWidth: 450,
    lineSpacing: 12,
    maxLines: 10,
    pageIndex: 0
  },

  'post_procedure_orders.position_in_bed': {
    x: 150,
    y: 250,
    size: 10,
    maxWidth: 300,
    pageIndex: 0
  },

  'post_procedure_orders.dressings': {
    x: 150,
    y: 220,
    size: 10,
    maxWidth: 300,
    pageIndex: 0
  },

  'post_procedure_orders.drug_and_iv_therapy': {
    x: 150,
    y: 190,
    size: 10,
    maxWidth: 300,
    pageIndex: 0
  },

  'post_procedure_orders.drain_tubes.present': {
    x: 200,
    y: 160,
    size: 12,
    pageIndex: 0
  },

  'post_procedure_orders.drain_tubes.type': {
    x: 250,
    y: 160,
    size: 10,
    maxWidth: 200,
    pageIndex: 0
  }
};

/**
 * Alternative configuration for different PDF templates
 * You can create multiple configs for different form layouts
 */
export const alternativeConfig = {
  // Different coordinate mappings for different PDF templates
  'operation_report.date': {
    x: 200,
    y: 800,
    size: 12,
    pageIndex: 0
  },
  // ... other mappings
};

/**
 * Utility function to adjust coordinates based on page dimensions
 * @param {Object} config - Original configuration
 * @param {Object} pageDimensions - Page dimensions {width, height}
 * @param {Object} adjustments - Coordinate adjustments
 * @returns {Object} Adjusted configuration
 */
export function adjustCoordinates(config, pageDimensions, adjustments = {}) {
  const { offsetX = 0, offsetY = 0, scaleX = 1, scaleY = 1 } = adjustments;

  const adjustedConfig = {};

  for (const [field, options] of Object.entries(config)) {
    adjustedConfig[field] = {
      ...options,
      x: (options.x + offsetX) * scaleX,
      y: (options.y + offsetY) * scaleY
    };
  }

  return adjustedConfig;
}

/**
 * Helper function to create coordinate mappings for form fields
 * @param {Array} fields - Array of field definitions
 * @returns {Object} Configuration object
 */
export function createFieldMapping(fields) {
  const config = {};

  fields.forEach(field => {
    config[field.path] = {
      x: field.x,
      y: field.y,
      size: field.size || 10,
      pageIndex: field.pageIndex || 0,
      ...field.options
    };
  });

  return config;
}

export default surgicalReportConfig;
